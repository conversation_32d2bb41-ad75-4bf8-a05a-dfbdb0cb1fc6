import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // Build optimization for production
  build: {
    // Output directory (default is 'dist')
    outDir: 'dist',

    // Generate source maps for debugging in production
    sourcemap: true,

    // Optimize bundle size
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunk for React and related libraries
          vendor: ['react', 'react-dom', 'react-router-dom'],

          // Firebase chunk for Firebase-related code
          firebase: ['firebase/app', 'firebase/auth', 'firebase/firestore']
        }
      }
    },

    // Minify for production
    minify: 'terser',

    // Target modern browsers for better optimization
    target: 'es2015',

    // Chunk size warning limit
    chunkSizeWarningLimit: 1000
  },

  // Development server configuration
  server: {
    port: 3000,
    open: true
  },

  // Preview server configuration
  preview: {
    port: 4173,
    open: true
  },

  // Define global constants
  define: {
    // Ensure process.env is available for compatibility
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production')
  }
})
