/* Community Section Styles - Future-Proof Design */
.community-section {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: var(--space-2xl) var(--space-lg);
}

.community-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.community-container h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 var(--space-2xl) 0;
}

/* Launch Mode Styles */
.launch-mode .launch-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2xl);
  align-items: start;
  margin-bottom: var(--space-2xl);
}

.launch-message {
  text-align: left;
}

.launch-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
}

.launch-message h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-secondary);
}

.launch-message p {
  font-size: var(--text-lg);
  line-height: 1.7;
  opacity: 0.95;
  margin: 0;
}

.launch-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.launch-feature {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  text-align: left;
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.feature-emoji {
  font-size: 2rem;
  flex-shrink: 0;
}

.launch-feature h4 {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-secondary);
}

.launch-feature p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.stats-note {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--color-secondary);
}

.stats-note p {
  margin: 0;
  font-style: italic;
  opacity: 0.9;
  color: #ecf0f1;
}

/* Stats Mode Styles */
.stats-mode .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-xl);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  transition: transform var(--transition-normal);
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-number {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  color: var(--color-secondary);
  margin-bottom: var(--space-sm);
  display: block;
}

.stat-label {
  font-size: var(--text-lg);
  opacity: 0.9;
  font-weight: 500;
}

/* Community Highlights (for future use) */
.community-highlights {
  margin-top: var(--space-2xl);
}

.community-highlights h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
  margin: 0 0 var(--space-xl) 0;
  color: var(--color-secondary);
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.highlight-item {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-xl);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  text-align: center;
  transition: transform var(--transition-normal);
}

.highlight-item:hover {
  transform: translateY(-3px);
}

.highlight-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

.highlight-item h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin: 0 0 var(--space-sm) 0;
  color: var(--color-secondary);
}

.highlight-item p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .launch-mode .launch-content {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .launch-message {
    text-align: center;
  }

  .stats-mode .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .highlights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .community-section {
    padding: var(--space-xl) var(--space-md);
  }

  .launch-features {
    gap: var(--space-md);
  }

  .launch-feature {
    padding: var(--space-md);
  }

  .stats-mode .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: var(--space-lg);
  }
}

/* Dark mode support */
.dark-mode .community-section {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.dark-mode .launch-feature,
.dark-mode .stat-item,
.dark-mode .highlight-item,
.dark-mode .stats-note {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .community-section {
    background: #000;
    color: #fff;
  }

  .launch-feature,
  .stat-item,
  .highlight-item,
  .stats-note {
    background: #333;
    border: 2px solid #fff;
  }
}
