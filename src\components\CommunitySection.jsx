import React from 'react';
import './CommunitySection.css';

/**
 * Community Section Component
 * Displays either launch messaging for new platform or real statistics when available
 * This component is designed to be easily switchable as the platform grows
 */
const CommunitySection = ({ 
  showStats = false, 
  stats = null,
  launchMode = true 
}) => {
  // Real statistics mode - when we have actual data
  if (showStats && stats) {
    return (
      <section className="community-section stats-mode">
        <div className="community-container">
          <h2>Join Our Growing Community</h2>
          <div className="stats-grid">
            {stats.stories && (
              <div className="stat-item">
                <div className="stat-number">{stats.stories.toLocaleString()}+</div>
                <div className="stat-label">Stories Shared</div>
              </div>
            )}
            {stats.members && (
              <div className="stat-item">
                <div className="stat-number">{stats.members.toLocaleString()}+</div>
                <div className="stat-label">Community Members</div>
              </div>
            )}
            {stats.successStories && (
              <div className="stat-item">
                <div className="stat-number">{stats.successStories.toLocaleString()}+</div>
                <div className="stat-label">Success Stories</div>
              </div>
            )}
            {stats.support && (
              <div className="stat-item">
                <div className="stat-number">{stats.support}</div>
                <div className="stat-label">Community Support</div>
              </div>
            )}
          </div>
          
          {/* Community highlights section for future use */}
          {stats.highlights && stats.highlights.length > 0 && (
            <div className="community-highlights">
              <h3>Community Highlights</h3>
              <div className="highlights-grid">
                {stats.highlights.map((highlight, index) => (
                  <div key={index} className="highlight-item">
                    <div className="highlight-icon">{highlight.icon}</div>
                    <h4>{highlight.title}</h4>
                    <p>{highlight.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>
    );
  }

  // Launch mode - for new platform
  return (
    <section className="community-section launch-mode">
      <div className="community-container">
        <h2>Be Part of Something Special</h2>
        <div className="launch-content">
          <div className="launch-message">
            <div className="launch-icon">🚀</div>
            <h3>You're Among Our Founding Members!</h3>
            <p>
              NAROOP is just beginning its journey, and you have the unique opportunity 
              to be part of our foundation. As one of our early adopters, you'll help 
              shape the future of this platform and witness its growth from the very start.
            </p>
          </div>
          
          <div className="launch-features">
            <div className="launch-feature">
              <span className="feature-emoji">🌱</span>
              <div>
                <h4>Growing Together</h4>
                <p>Watch as our community grows and evolves with your contributions</p>
              </div>
            </div>
            <div className="launch-feature">
              <span className="feature-emoji">💎</span>
              <div>
                <h4>Founding Member Status</h4>
                <p>Special recognition as one of NAROOP's original community builders</p>
              </div>
            </div>
            <div className="launch-feature">
              <span className="feature-emoji">🎯</span>
              <div>
                <h4>Shape the Future</h4>
                <p>Your feedback and participation will directly influence our development</p>
              </div>
            </div>
            <div className="launch-feature">
              <span className="feature-emoji">🤝</span>
              <div>
                <h4>Close-Knit Community</h4>
                <p>Experience the intimacy of a growing community where every voice matters</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="stats-note">
          <p>
            <em>Community statistics and highlights will be added here as our platform grows. 
            You'll be part of creating those numbers!</em>
          </p>
        </div>
      </div>
    </section>
  );
};

export default CommunitySection;

/**
 * Example usage when real stats become available:
 * 
 * const realStats = {
 *   stories: 1250,
 *   members: 850,
 *   successStories: 75,
 *   support: "24/7",
 *   highlights: [
 *     {
 *       icon: "🏆",
 *       title: "Story of the Month",
 *       description: "Amazing stories shared by our community"
 *     },
 *     {
 *       icon: "💼",
 *       title: "Business Connections",
 *       description: "Members finding opportunities through networking"
 *     }
 *   ]
 * };
 * 
 * <CommunitySection showStats={true} stats={realStats} launchMode={false} />
 */
