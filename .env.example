# Firebase Configuration
# Copy this file to .env.local for local development
# For Netlify production, set these in your Netlify dashboard under Site Settings > Environment Variables
# Get these values from your Firebase project settings

VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Application Settings
VITE_APP_ENV=development
VITE_APP_VERSION=1.0.0
VITE_APP_NAME=Naroop

# Build Settings (for Netlify)
NODE_ENV=production
NODE_VERSION=18

# Optional: Analytics and Monitoring
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true

# Optional: Feature Flags
VITE_ENABLE_KIDS_SECTION=true
VITE_ENABLE_COMMUNITY_FEATURES=true
VITE_ENABLE_MESSAGING=true
