{"name": "naroop", "private": true, "version": "1.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "vite build --mode analyze", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "preview:build": "npm run build && npm run preview", "clean": "rm -rf dist", "test:build": "npm run build && npm run preview"}, "dependencies": {"firebase": "^11.9.1", "react": "^18.3.0", "react-dom": "^18.3.0", "react-router-dom": "^6.26.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "terser": "^5.43.1", "vite": "^5.4.0"}}