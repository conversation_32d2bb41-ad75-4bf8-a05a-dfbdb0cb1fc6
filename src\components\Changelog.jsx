import React, { useState, useEffect } from 'react';
import { subscribeToChangelog, CATEGORY_INFO } from '../services/changelog';
import { getFormattedVersion } from '../utils/version';
import { addV110ChangelogEntry } from '../utils/addNewChangelog';
import './Changelog.css';

const Changelog = ({ onClose }) => {
  const [changelogEntries, setChangelogEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [addingEntry, setAddingEntry] = useState(false);

  useEffect(() => {
    let unsubscribe;

    const setupChangelog = async () => {
      try {
        unsubscribe = subscribeToChangelog((entries) => {
          console.log('Changelog entries received:', entries);
          setChangelogEntries(entries);
          setLoading(false);
          setError(null);
        });
      } catch (err) {
        console.error('Error setting up changelog subscription:', err);
        setError('Failed to load changelog. Please try again later.');
        setLoading(false);
      }
    };

    // Add a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (loading) {
        console.log('Changelog loading timeout - setting empty state');
        setChangelogEntries([]);
        setLoading(false);
        setError(null);
      }
    }, 10000); // 10 second timeout

    setupChangelog();

    return () => {
      clearTimeout(timeoutId);
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [loading]);

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return 'Unknown date';
    }
  };

  const filteredEntries = selectedCategory === 'all' 
    ? changelogEntries 
    : changelogEntries.filter(entry => entry.category === selectedCategory);

  const categories = Object.keys(CATEGORY_INFO);

  if (loading) {
    return (
      <div className="changelog-overlay" onClick={onClose}>
        <div className="changelog-modal" onClick={(e) => e.stopPropagation()}>
          <div className="changelog-header">
            <div className="changelog-title">
              <h2>📋 Changelog</h2>
              <p>Loading updates...</p>
            </div>
            <button className="changelog-close" onClick={onClose} aria-label="Close changelog">
              ✕
            </button>
          </div>
          <div className="changelog-loading">
            <div className="loading-spinner"></div>
            <p>Loading changelog...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="changelog-overlay" onClick={onClose}>
        <div className="changelog-modal" onClick={(e) => e.stopPropagation()}>
          <div className="changelog-header">
            <div className="changelog-title">
              <h2>📋 Changelog</h2>
              <p>What's new in NAROOP</p>
            </div>
            <button className="changelog-close" onClick={onClose} aria-label="Close changelog">
              ✕
            </button>
          </div>
          <div className="changelog-error">
            <div className="error-icon">⚠️</div>
            <h3>Unable to Load Updates</h3>
            <p>{error}</p>
            <button className="retry-btn" onClick={() => window.location.reload()}>
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="changelog-overlay" onClick={onClose}>
      <div className="changelog-modal" onClick={(e) => e.stopPropagation()}>
        <div className="changelog-header">
          <div className="changelog-title">
            <h2>📋 Changelog</h2>
            <p>What's new in NAROOP</p>
          </div>
          <button className="changelog-close" onClick={onClose} aria-label="Close changelog">
            ✕
          </button>
        </div>

        <div className="changelog-filters">
          <button
            className={`filter-btn ${selectedCategory === 'all' ? 'active' : ''}`}
            onClick={() => setSelectedCategory('all')}
          >
            All Updates
          </button>
          {categories.map(category => (
            <button
              key={category}
              className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category)}
            >
              {CATEGORY_INFO[category].icon} {CATEGORY_INFO[category].label}
            </button>
          ))}
        </div>

        <div className="changelog-content">
          {filteredEntries.length === 0 ? (
            <div className="changelog-empty">
              <div className="empty-icon">🚀</div>
              <h3>Welcome to NAROOP!</h3>
              <p>
                This is where you'll find all the latest updates, new features, and improvements
                to the NAROOP platform. As we continue to grow and enhance your experience,
                all changes will be documented here.
              </p>
              <p>
                <strong>You're among our founding members!</strong> Thank you for being part
                of our journey from the very beginning.
              </p>
              <div className="coming-soon">
                <h4>Coming Soon:</h4>
                <ul>
                  <li>Platform updates and new features</li>
                  <li>Community enhancements</li>
                  <li>Bug fixes and improvements</li>
                  <li>Security updates</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className="changelog-timeline">
              {filteredEntries.map((entry, index) => (
                <div key={entry.id} className="changelog-entry">
                  <div className="entry-marker">
                    <div 
                      className="marker-dot"
                      style={{ backgroundColor: CATEGORY_INFO[entry.category]?.color || '#3498db' }}
                    >
                      {CATEGORY_INFO[entry.category]?.icon || '📝'}
                    </div>
                    {index < filteredEntries.length - 1 && <div className="marker-line"></div>}
                  </div>

                  <div className="entry-content">
                    <div className="entry-header">
                      <div className="entry-version">
                        <span className="version-badge">v{entry.version}</span>
                        <span className="category-badge" style={{ 
                          backgroundColor: CATEGORY_INFO[entry.category]?.color || '#3498db' 
                        }}>
                          {CATEGORY_INFO[entry.category]?.icon} {CATEGORY_INFO[entry.category]?.label}
                        </span>
                      </div>
                      <div className="entry-date">
                        {formatDate(entry.releaseDate)}
                      </div>
                    </div>

                    <h3 className="entry-title">{entry.title}</h3>
                    <p className="entry-description">{entry.description}</p>

                    {entry.changes && entry.changes.length > 0 && (
                      <div className="entry-changes">
                        <h4>Changes:</h4>
                        <ul>
                          {entry.changes.map((change, changeIndex) => (
                            <li key={changeIndex}>{change}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Temporary admin button to add v1.1.0 changelog entry */}
        <div style={{ padding: '1rem', textAlign: 'center', borderTop: '1px solid #eee' }}>
          <button
            onClick={async () => {
              setAddingEntry(true);
              try {
                const result = await addV110ChangelogEntry();
                if (result.success) {
                  alert('✅ Successfully added v1.1.0 changelog entry!');
                } else {
                  alert('❌ Error: ' + result.error);
                }
              } catch (error) {
                alert('❌ Error adding changelog entry: ' + error.message);
              }
              setAddingEntry(false);
            }}
            disabled={addingEntry}
            style={{
              background: '#34d399',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '8px',
              cursor: addingEntry ? 'not-allowed' : 'pointer',
              opacity: addingEntry ? 0.6 : 1
            }}
          >
            {addingEntry ? 'Adding...' : '🌱 Add v1.1.0 Green Theme Update'}
          </button>
          <p style={{ fontSize: '0.8rem', color: '#666', marginTop: '0.5rem' }}>
            Click once to add the new changelog entry for the green theme update
          </p>
        </div>

        <div className="changelog-footer">
          <p>
            Current version: <strong>{getFormattedVersion()}</strong>
          </p>
          <p className="footer-note">
            Stay tuned for more updates as we continue to improve NAROOP!
          </p>
        </div>
      </div>
    </div>
  );
};

export default Changelog;
