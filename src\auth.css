/* Auth form styles for NAROOP */
body:has(.auth-form) {
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(34, 139, 34, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

.auth-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  max-width: 420px;
  margin: 80px auto 0 auto;
  padding: 3rem 2.5rem 2rem 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.back-to-landing {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 215, 0, 0.3);
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.back-to-landing:hover {
  background: rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.5);
  color: white;
  transform: translateY(-2px);
}
/* Auth Brand Section */
.auth-brand {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
  animation: pulse 2s ease-in-out infinite;
}

.auth-form h2 {
  background: linear-gradient(45deg, #1a1a2e, #533483);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  font-weight: 900;
  letter-spacing: -0.02em;
  text-align: center;
}

.auth-subtitle {
  color: rgba(26, 26, 46, 0.7);
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0;
  text-align: center;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
/* Input Groups */
.input-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.auth-form input {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  color: #1a1a2e;
  font-weight: 500;
  box-sizing: border-box;
}

.auth-form input::placeholder {
  color: rgba(26, 26, 46, 0.6);
  font-weight: 400;
}

.auth-form input:focus {
  border: 2px solid rgba(255, 215, 0, 0.8);
  outline: none;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.1);
  transform: translateY(-2px);
}
.auth-form button[type="submit"] {
  width: 100%;
  background: linear-gradient(135deg, #533483 0%, #7209b7 100%);
  color: #fff;
  border: none;
  border-radius: 16px;
  padding: 1rem 0;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(83, 52, 131, 0.3);
  position: relative;
  overflow: hidden;
}

.auth-form button[type="submit"]:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
  transition: left 0.5s ease;
}

.auth-form button[type="submit"]:hover {
  background: linear-gradient(135deg, #7209b7 0%, #533483 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(83, 52, 131, 0.4);
}

.auth-form button[type="submit"]:hover:before {
  left: 100%;
}

.auth-form button[type="submit"]:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}
/* Auth Links Section */
.auth-links {
  margin-top: 1.5rem;
  text-align: center;
}

.auth-links p {
  margin: 0.75rem 0;
  font-size: 1rem;
  color: rgba(26, 26, 46, 0.8);
  font-weight: 500;
}

.auth-form .auth-error {
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.2);
  border-radius: 12px;
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  width: 100%;
  text-align: center;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.auth-form button:not([type="submit"]) {
  background: none;
  border: none;
  color: #7209b7;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.auth-form button:not([type="submit"]):hover {
  color: #533483;
  background: rgba(114, 9, 183, 0.1);
  transform: translateY(-1px);
}

.auth-form button:focus {
  outline: 2px solid rgba(255, 215, 0, 0.6);
  outline-offset: 2px;
}

/* Add floating animation for the form */
.auth-form {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Add subtle glow effect */
.auth-form::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    rgba(255, 215, 0, 0.3),
    rgba(83, 52, 131, 0.3),
    rgba(114, 9, 183, 0.3),
    rgba(255, 215, 0, 0.3)
  );
  border-radius: 26px;
  z-index: -1;
  filter: blur(6px);
  opacity: 0.7;
  animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
  0% {
    opacity: 0.5;
    filter: blur(6px);
  }
  100% {
    opacity: 0.8;
    filter: blur(8px);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .auth-form {
    margin: 40px auto 0 auto;
    max-width: 90%;
    padding: 2rem 1.5rem 1.5rem 1.5rem;
  }

  .back-to-landing {
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
}

.naroop-auth-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem 0 2rem;
}
.naroop-logout-btn {
  background: #e63946;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.naroop-logout-btn:hover {
  background: #fbbf24;
  color: #22223b;
}

.naroop-account-link {
  background: #3a86ff;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  text-decoration: none;
  display: inline-block;
}

.naroop-account-link:hover {
  background: #2563eb;
  color: #fff;
}

/* Account Page Styles */
.account-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f7fafc 0%, #fbbf24 100%);
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.account-header {
  background: linear-gradient(90deg, #222 0%, #e63946 60%, #fbbf24 100%);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: 0 4px 16px rgba(26,26,46,0.08);
}

.account-header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.account-header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.back-to-home-btn, .logout-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.back-to-home-btn:hover, .logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.account-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(251, 191, 36, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f8f9fa;
}

.section-header h2 {
  margin: 0;
  color: #22223b;
  font-size: 1.5rem;
  font-weight: 600;
}

.edit-profile-btn {
  background: #3a86ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-profile-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Profile Display Styles */
.profile-display {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.profile-avatar {
  font-size: 4rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.profile-basic-info h3 {
  margin: 0 0 0.5rem 0;
  color: #22223b;
  font-size: 1.8rem;
  font-weight: 700;
}

.profile-email {
  color: #6b7280;
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
}

.profile-join-date {
  color: #9ca3af;
  margin: 0;
  font-size: 0.9rem;
}

.profile-bio {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #fbbf24;
}

.profile-bio h4 {
  margin: 0 0 0.75rem 0;
  color: #22223b;
  font-size: 1.1rem;
  font-weight: 600;
}

.profile-bio p {
  margin: 0;
  color: #4b5563;
  line-height: 1.6;
}

/* Profile Edit Form Styles */
.profile-edit-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #22223b;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: #f9fafb;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #fbbf24;
  background: white;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn, .cancel-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn {
  background: #10b981;
  color: white;
}

.save-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.cancel-btn {
  background: #6b7280;
  color: white;
}

.cancel-btn:hover {
  background: #4b5563;
}

/* Community Profile Styles */
.community-profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.community-field {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.25rem;
  border-left: 4px solid #e63946;
}

.community-field h4 {
  margin: 0 0 0.75rem 0;
  color: #22223b;
  font-size: 1rem;
  font-weight: 600;
}

.community-field p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #22223b;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.mentor-status {
  font-weight: 600;
}

.mentor-status.available {
  color: #10b981;
}

.mentor-status.unavailable {
  color: #6b7280;
}

/* Statistics Section Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.stat-card > * {
  position: relative;
  z-index: 2;
}

.stat-card:hover {
  transform: translateY(-4px);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  display: block;
  background: rgba(255, 255, 255, 0.2);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem auto;
  backdrop-filter: blur(10px);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 0.5rem;
  display: block;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.stat-label {
  font-size: 0.9rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  color: #ffffff;
  opacity: 0.95;
}

/* Badges Section */
.badges-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #fbbf24;
}

.badges-section h4 {
  margin: 0 0 1rem 0;
  color: #22223b;
  font-size: 1.1rem;
  font-weight: 600;
}

.badges-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.badge {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid #fbbf24;
}

.badge-icon {
  font-size: 1.5rem;
}

.badge-name {
  font-weight: 600;
  color: #22223b;
}

/* Settings Section */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.setting-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.25rem;
  border-left: 4px solid #3a86ff;
}

.setting-item h4 {
  margin: 0 0 0.5rem 0;
  color: #22223b;
  font-size: 1rem;
  font-weight: 600;
}

.setting-item p {
  margin: 0 0 0.5rem 0;
  color: #4b5563;
  font-weight: 500;
}

.setting-item small {
  color: #9ca3af;
  font-size: 0.85rem;
}

/* Loading and Error States */
.account-loading, .account-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #fbbf24;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.account-error h2 {
  color: #e63946;
  margin-bottom: 1rem;
}

.account-error button {
  background: #e63946;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.account-error button:hover {
  background: #dc2626;
}

/* Responsive Design */
@media (max-width: 768px) {
  .account-header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .account-header h1 {
    font-size: 1.5rem;
  }

  .account-content {
    padding: 1rem;
  }

  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .community-profile-grid,
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .badges-list {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .back-to-home-btn, .logout-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}
