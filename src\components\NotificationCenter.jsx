import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import {
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  getUnreadNotificationCount,
  updateNotificationPreferences,
  getNotificationPreferences
} from '../services/notifications';

/**
 * Notification Center Component
 * Displays and manages user notifications with preferences
 */
export default function NotificationCenter({ onClose }) {
  const { currentUser } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // 'all', 'unread', 'messages', 'support', 'stories', 'campaigns'
  const [showPreferences, setShowPreferences] = useState(false);
  const [preferences, setPreferences] = useState(null);

  useEffect(() => {
    if (!currentUser) return;

    let unsubscribeNotifications = () => {};
    let unsubscribeUnread = () => {};

    // Load notifications with error handling
    try {
      unsubscribeNotifications = getUserNotifications(
        currentUser.uid,
        (notifs) => {
          setNotifications(notifs);
          setLoading(false);
        },
        { limit: 100 }
      );
    } catch (error) {
      console.error('Error setting up notifications listener:', error);
      setNotifications([]);
      setLoading(false);
    }

    // Load unread count with error handling
    try {
      unsubscribeUnread = getUnreadNotificationCount(currentUser.uid, setUnreadCount);
    } catch (error) {
      console.error('Error setting up unread count listener:', error);
      setUnreadCount(0);
    }

    // Load preferences
    loadPreferences();

    // Set a timeout to ensure loading state doesn't persist indefinitely
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
        setNotifications([]);
      }
    }, 5000); // 5 second timeout

    return () => {
      unsubscribeNotifications();
      unsubscribeUnread();
      clearTimeout(loadingTimeout);
    };
  }, [currentUser]);

  const loadPreferences = async () => {
    try {
      const prefs = await getNotificationPreferences(currentUser.uid);
      setPreferences(prefs);
    } catch (error) {
      console.error('Error loading preferences:', error);
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !notification.read;
      case 'messages':
        return notification.category === 'message';
      case 'support':
        return notification.category === 'support';
      case 'stories':
        return notification.category === 'story';
      case 'campaigns':
        return notification.category === 'campaign';
      default:
        return true;
    }
  });

  const handleNotificationClick = async (notification) => {
    if (!notification.read) {
      await markNotificationAsRead(notification.id);
    }

    // Navigate to action URL if provided
    if (notification.actionUrl) {
      console.log('Navigate to:', notification.actionUrl);
      // In a real implementation, this would use router navigation
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsAsRead(currentUser.uid);
    } catch (error) {
      console.error('Error marking all as read:', error);
    }
  };

  const handleDeleteNotification = async (notificationId) => {
    try {
      await deleteNotification(notificationId, currentUser.uid);
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  const handleUpdatePreferences = async (newPreferences) => {
    try {
      await updateNotificationPreferences(currentUser.uid, newPreferences);
      setPreferences(newPreferences);
      setShowPreferences(false);
    } catch (error) {
      console.error('Error updating preferences:', error);
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now - timestamp) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return timestamp.toLocaleDateString();
  };

  const getNotificationIcon = (type, category) => {
    switch (category) {
      case 'message': return '💬';
      case 'support': return '🤝';
      case 'story': return '📖';
      case 'campaign': return '✊🏾';
      case 'system': return '⚙️';
      default: return '🔔';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return '#dc2626';
      case 'high': return '#f59e0b';
      case 'normal': return '#3b82f6';
      case 'low': return '#6b7280';
      default: return '#6b7280';
    }
  };

  if (loading) {
    return (
      <div className="notification-center">
        <div className="notification-loading">
          <div className="loading-spinner"></div>
          <p>Loading notifications...</p>
        </div>
      </div>
    );
  }

  if (showPreferences) {
    return (
      <NotificationPreferences
        preferences={preferences}
        onSave={handleUpdatePreferences}
        onCancel={() => setShowPreferences(false)}
      />
    );
  }

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setTimeout(() => {
        try {
          onClose();
        } catch (error) {
          console.error('Error closing notification center:', error);
        }
      }, 50);
    }
  };

  return (
    <div className="notification-center" onClick={handleOverlayClick}>
      <div className="notification-content">
        <div className="notification-header">
          <h2>🔔 Notifications</h2>
          {unreadCount > 0 && (
            <span className="unread-badge">{unreadCount}</span>
          )}
          <div className="header-actions">
            <button
              className="preferences-btn"
              onClick={() => setShowPreferences(true)}
              title="Notification preferences"
            >
              ⚙️
            </button>
            <button className="close-notification-btn" onClick={onClose}>
              ✕
            </button>
          </div>
        </div>
        <div className="notification-controls">
          <div className="filter-tabs">
            {[
              { key: 'all', label: 'All', count: notifications.length },
              { key: 'unread', label: 'Unread', count: unreadCount },
              { key: 'messages', label: 'Messages', count: notifications.filter(n => n.category === 'message').length },
              { key: 'support', label: 'Support', count: notifications.filter(n => n.category === 'support').length }
            ].map(tab => (
              <button
                key={tab.key}
                className={`filter-tab ${filter === tab.key ? 'active' : ''}`}
                onClick={() => setFilter(tab.key)}
              >
                {tab.label} {tab.count > 0 && `(${tab.count})`}
              </button>
            ))}
          </div>

          {unreadCount > 0 && (
            <button
              className="mark-all-read-btn"
              onClick={handleMarkAllAsRead}
            >
              Mark All Read
            </button>
          )}
        </div>

        <div className="notifications-list">
          {filteredNotifications.length > 0 ? (
            filteredNotifications.map(notification => (
              <div
                key={notification.id}
                className={`notification-item ${!notification.read ? 'unread' : ''}`}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="notification-icon">
                  {getNotificationIcon(notification.type, notification.category)}
                </div>
                
                <div className="notification-content-area">
                  <div className="notification-header-info">
                    <h4 className="notification-title">{notification.title}</h4>
                    <div className="notification-meta">
                      <span 
                        className="priority-indicator"
                        style={{ backgroundColor: getPriorityColor(notification.priority) }}
                      ></span>
                      <span className="notification-time">
                        {formatTimeAgo(notification.createdAt)}
                      </span>
                    </div>
                  </div>
                  
                  <p className="notification-message">{notification.message}</p>
                  
                  {!notification.read && (
                    <div className="unread-indicator"></div>
                  )}
                </div>

                <div className="notification-actions">
                  <button
                    className="delete-notification-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteNotification(notification.id);
                    }}
                    title="Delete notification"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="empty-notifications">
              <div className="empty-icon">🔔</div>
              <h3>No notifications</h3>
              <p>
                {filter === 'unread' 
                  ? "You're all caught up! No unread notifications."
                  : "You don't have any notifications yet."
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Notification Preferences Component
 */
function NotificationPreferences({ preferences, onSave, onCancel }) {
  const [localPreferences, setLocalPreferences] = useState(preferences);

  const handleToggle = (category, type, value) => {
    setLocalPreferences(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [type]: value
      }
    }));
  };

  const handleQuietHoursToggle = (enabled) => {
    setLocalPreferences(prev => ({
      ...prev,
      quietHours: {
        ...prev.quietHours,
        enabled
      }
    }));
  };

  const handleQuietHoursTime = (field, value) => {
    setLocalPreferences(prev => ({
      ...prev,
      quietHours: {
        ...prev.quietHours,
        [field]: value
      }
    }));
  };

  return (
    <div className="notification-preferences">
      <div className="preferences-header">
        <h2>🔔 Notification Preferences</h2>
        <button className="close-preferences-btn" onClick={onCancel}>
          ✕
        </button>
      </div>

      <div className="preferences-content">
        <div className="preference-section">
          <h3>Email Notifications</h3>
          <div className="preference-grid">
            {Object.entries(localPreferences.email).map(([key, value]) => (
              <label key={key} className="preference-item">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) => handleToggle('email', key, e.target.checked)}
                />
                <span className="preference-label">
                  {key.charAt(0).toUpperCase() + key.slice(1)}
                </span>
              </label>
            ))}
          </div>
        </div>

        <div className="preference-section">
          <h3>In-App Notifications</h3>
          <div className="preference-grid">
            {Object.entries(localPreferences.inApp).map(([key, value]) => (
              <label key={key} className="preference-item">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) => handleToggle('inApp', key, e.target.checked)}
                />
                <span className="preference-label">
                  {key.charAt(0).toUpperCase() + key.slice(1)}
                </span>
              </label>
            ))}
          </div>
        </div>

        <div className="preference-section">
          <h3>Quiet Hours</h3>
          <label className="preference-item">
            <input
              type="checkbox"
              checked={localPreferences.quietHours.enabled}
              onChange={(e) => handleQuietHoursToggle(e.target.checked)}
            />
            <span className="preference-label">Enable quiet hours</span>
          </label>
          
          {localPreferences.quietHours.enabled && (
            <div className="quiet-hours-config">
              <div className="time-input-group">
                <label>From:</label>
                <input
                  type="time"
                  value={localPreferences.quietHours.start}
                  onChange={(e) => handleQuietHoursTime('start', e.target.value)}
                />
              </div>
              <div className="time-input-group">
                <label>To:</label>
                <input
                  type="time"
                  value={localPreferences.quietHours.end}
                  onChange={(e) => handleQuietHoursTime('end', e.target.value)}
                />
              </div>
            </div>
          )}
        </div>

        <div className="preferences-actions">
          <button className="cancel-btn" onClick={onCancel}>
            Cancel
          </button>
          <button className="save-btn" onClick={() => onSave(localPreferences)}>
            Save Preferences
          </button>
        </div>
      </div>
    </div>
  );
}
