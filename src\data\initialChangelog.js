/**
 * Initial Changelog Data
 * Sample changelog entries for the Naroop application
 */

import { CHANGELOG_CATEGORIES } from '../services/changelog';

export const initialChangelogEntries = [
  {
    version: '1.0.0',
    title: 'Welcome to NAROOP 1.0!',
    description: 'The official launch of NAROOP (Narrative of Our People) - a platform dedicated to uplifting Black voices and building community through storytelling.',
    category: CHANGELOG_CATEGORIES.FEATURE,
    releaseDate: new Date().toISOString(),
    changes: [
      'Complete platform launch with user authentication',
      'Story sharing and community engagement features',
      'Economic empowerment hub with resources and tools',
      'Community dialogue and discussion forums',
      'Support network for community members',
      'Community activism and organizing tools',
      'Dedicated Kids Zone with age-appropriate content',
      'Task management and content organization',
      'Private messaging system',
      'Real-time notifications',
      'Dynamic community newsfeed',
      'Responsive design for all devices',
      'Accessibility features and high contrast support'
    ]
  },
  {
    version: '0.9.0',
    title: 'Pre-Launch Beta',
    description: 'Final beta testing phase with core features implemented and community feedback incorporated.',
    category: CHANGELOG_CATEGORIES.IMPROVEMENT,
    releaseDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
    changes: [
      'Enhanced mobile responsiveness across all components',
      'Improved accessibility with ARIA labels and keyboard navigation',
      'Optimized performance and loading times',
      'Refined UI/UX based on user feedback',
      'Added comprehensive error handling',
      'Implemented data validation and security measures'
    ]
  },
  {
    version: '0.8.0',
    title: 'Community Features Expansion',
    description: 'Major expansion of community-focused features to enhance user engagement and connection.',
    category: CHANGELOG_CATEGORIES.FEATURE,
    releaseDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 2 weeks ago
    changes: [
      'Added private messaging system for community members',
      'Implemented real-time notification center',
      'Created dynamic newsfeed with personalized content',
      'Enhanced user profiles with additional fields',
      'Added community connection and mentorship features',
      'Improved search and filtering capabilities'
    ]
  },
  {
    version: '0.7.0',
    title: 'Kids Zone Launch',
    description: 'Introduction of the dedicated Kids Zone with child-safe features and educational content.',
    category: CHANGELOG_CATEGORIES.FEATURE,
    releaseDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(), // 3 weeks ago
    changes: [
      'Launched dedicated Kids Zone with age-appropriate content',
      'Implemented COPPA compliance and parental controls',
      'Added educational games and interactive features',
      'Created child-safe UI with larger buttons and clear navigation',
      'Added positive messaging and role model content',
      'Implemented session time limits and safety features'
    ]
  },
  {
    version: '0.6.0',
    title: 'Economic Empowerment Hub',
    description: 'Launch of comprehensive economic empowerment features to support community financial growth.',
    category: CHANGELOG_CATEGORIES.FEATURE,
    releaseDate: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000).toISOString(), // 4 weeks ago
    changes: [
      'Created economic empowerment hub with resources',
      'Added business networking and mentorship features',
      'Implemented goal tracking and progress monitoring',
      'Added financial literacy resources and tools',
      'Created business opportunity sharing platform',
      'Integrated community support for economic initiatives'
    ]
  }
];

/**
 * Function to populate initial changelog data
 * This would typically be run once during setup
 */
export const populateInitialChangelog = async () => {
  try {
    const { addChangelogEntry } = await import('../services/changelog');
    
    for (const entry of initialChangelogEntries) {
      await addChangelogEntry(entry);
    }
    
    console.log('Initial changelog data populated successfully');
  } catch (error) {
    console.error('Error populating initial changelog:', error);
  }
};
